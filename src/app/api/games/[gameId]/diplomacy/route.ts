import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { GameService } from '@/lib/database/games';
import { getDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ gameId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { gameId: gameIdParam } = await params;
    const gameId = new ObjectId(gameIdParam);
    const userId = new ObjectId(session.user.id);

    // Verify user is in the game
    const game = await GameService.getGame(gameId);
    if (!game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    const player = game.players.find(p => p.userId.equals(userId));
    if (!player) {
      return NextResponse.json(
        { error: 'Player not in game' },
        { status: 403 }
      );
    }

    // Get diplomatic proposals for this player
    const db = await getDatabase();
    const proposals = await db.collection('diplomatic_proposals').find({
      gameId,
      $or: [
        { fromPlayerId: userId },
        { toPlayerId: userId }
      ]
    }).toArray();

    return NextResponse.json(proposals);
  } catch (error) {
    console.error('Error fetching diplomatic proposals:', error);
    return NextResponse.json(
      { error: 'Failed to fetch diplomatic proposals' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ gameId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { targetPlayerId, type, message } = await request.json();

    if (!targetPlayerId || !type || !message) {
      return NextResponse.json(
        { error: 'Target player, type, and message are required' },
        { status: 400 }
      );
    }

    const { gameId: gameIdParam } = await params;
    const gameId = new ObjectId(gameIdParam);
    const fromPlayerId = new ObjectId(session.user.id);
    const toPlayerId = new ObjectId(targetPlayerId);

    // Verify user is in the game
    const game = await GameService.getGame(gameId);
    if (!game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    const fromPlayer = game.players.find(p => p.userId.equals(fromPlayerId));
    const toPlayer = game.players.find(p => p.userId.equals(toPlayerId));

    if (!fromPlayer || !toPlayer) {
      return NextResponse.json(
        { error: 'One or both players not found in game' },
        { status: 400 }
      );
    }

    // Create diplomatic proposal
    const db = await getDatabase();
    const proposal = {
      gameId,
      fromPlayerId,
      toPlayerId,
      fromPlayerName: fromPlayer.username,
      toPlayerName: toPlayer.username,
      type,
      message,
      status: 'pending',
      createdAt: new Date()
    };

    await db.collection('diplomatic_proposals').insertOne(proposal);

    return NextResponse.json(
      { message: 'Diplomatic proposal sent successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error sending diplomatic proposal:', error);
    return NextResponse.json(
      { error: 'Failed to send diplomatic proposal' },
      { status: 500 }
    );
  }
}
