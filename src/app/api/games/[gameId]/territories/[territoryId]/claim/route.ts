import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { TerritoryService } from '@/lib/database/territories';
import { GameService } from '@/lib/database/games';
import { ObjectId } from 'mongodb';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ gameId: string; territoryId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { gameId: gameIdParam, territoryId: territoryIdParam } = await params;
    const gameId = new ObjectId(gameIdParam);
    const territoryId = new ObjectId(territoryIdParam);
    const userId = new ObjectId(session.user.id);

    // Verify user is in the game
    const game = await GameService.getGame(gameId);
    if (!game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    const player = game.players.find(p => p.userId.equals(userId));
    if (!player) {
      return NextResponse.json(
        { error: 'Player not in game' },
        { status: 403 }
      );
    }

    // Check if game is active
    if (game.status !== 'active') {
      return NextResponse.json(
        { error: 'Game is not active' },
        { status: 400 }
      );
    }

    // Attempt to claim the territory
    const success = await TerritoryService.claimTerritory(territoryId, userId);

    if (!success) {
      return NextResponse.json(
        { error: 'Territory is already claimed or does not exist' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'Territory claimed successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error claiming territory:', error);
    return NextResponse.json(
      { error: 'Failed to claim territory' },
      { status: 500 }
    );
  }
}
