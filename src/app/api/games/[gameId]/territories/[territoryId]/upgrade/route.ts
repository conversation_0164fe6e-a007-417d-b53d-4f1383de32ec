import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { TerritoryService } from '@/lib/database/territories';
import { GameService } from '@/lib/database/games';
import { ObjectId } from 'mongodb';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ gameId: string; territoryId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { upgradeType } = await request.json();

    if (!upgradeType) {
      return NextResponse.json(
        { error: 'Upgrade type is required' },
        { status: 400 }
      );
    }

    const { gameId: gameIdParam, territoryId: territoryIdParam } = await params;
    const gameId = new ObjectId(gameIdParam);
    const territoryId = new ObjectId(territoryIdParam);
    const userId = new ObjectId(session.user.id);

    // Verify user is in the game
    const game = await GameService.getGame(gameId);
    if (!game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    const player = game.players.find(p => p.userId.equals(userId));
    if (!player) {
      return NextResponse.json(
        { error: 'Player not in game' },
        { status: 403 }
      );
    }

    // Check if game is active
    if (game.status !== 'active') {
      return NextResponse.json(
        { error: 'Game is not active' },
        { status: 400 }
      );
    }

    // Verify territory ownership
    const territory = await TerritoryService.getTerritoryById(territoryId);
    if (!territory) {
      return NextResponse.json(
        { error: 'Territory not found' },
        { status: 404 }
      );
    }

    if (!territory.ownerId || !territory.ownerId.equals(userId)) {
      return NextResponse.json(
        { error: 'You do not own this territory' },
        { status: 403 }
      );
    }

    // Check if player has enough resources (simplified - would need more complex logic)
    const upgradeCosts: { [key: string]: { [resource: string]: number } } = {
      defense: { oil: 50, minerals: 30 },
      population: { food: 100, water: 50 },
      oil_production: { minerals: 80, food: 20 },
      mineral_production: { oil: 60, food: 30 },
      water_production: { oil: 40, minerals: 60 },
      food_production: { water: 80, minerals: 40 },
    };

    const cost = upgradeCosts[upgradeType];
    if (!cost) {
      return NextResponse.json(
        { error: 'Invalid upgrade type' },
        { status: 400 }
      );
    }

    // Check if player has enough resources
    for (const [resource, amount] of Object.entries(cost)) {
      if (player.resources[resource as keyof typeof player.resources] < amount) {
        return NextResponse.json(
          { error: `Insufficient ${resource}. Need ${amount}, have ${player.resources[resource as keyof typeof player.resources]}` },
          { status: 400 }
        );
      }
    }

    // Perform the upgrade
    const success = await TerritoryService.upgradeTerritory(territoryId, upgradeType);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to upgrade territory' },
        { status: 500 }
      );
    }

    // Deduct resources from player (would need to implement this in GameService)
    // For now, we'll just return success

    return NextResponse.json(
      { message: 'Territory upgraded successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error upgrading territory:', error);
    return NextResponse.json(
      { error: 'Failed to upgrade territory' },
      { status: 500 }
    );
  }
}
