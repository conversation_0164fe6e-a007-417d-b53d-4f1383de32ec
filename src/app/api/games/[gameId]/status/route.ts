import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { GameService } from '@/lib/database/games';
import { ObjectId } from 'mongodb';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ gameId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { gameId: gameIdParam } = await params;
    const gameId = new ObjectId(gameIdParam);
    const userId = new ObjectId(session.user.id);

    const game = await GameService.getGame(gameId);
    if (!game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    // Check if user is already in the game
    const player = game.players.find(p => p.userId.equals(userId));
    const isPlayerInGame = !!player;

    return NextResponse.json({
      gameId: game._id?.toString(),
      gameName: game.name,
      gameStatus: game.status,
      isPlayerInGame,
      playerInfo: player ? {
        username: player.username,
        tribeId: player.tribeId,
        isTribalLeader: player.isTribalLeader,
        isAlive: player.isAlive
      } : null
    });
  } catch (error) {
    console.error('Error checking game status:', error);
    return NextResponse.json(
      { error: 'Failed to check game status' },
      { status: 500 }
    );
  }
}
