import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { GameService } from '@/lib/database/games';
import { TerritoryService } from '@/lib/database/territories';
import { getDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ gameId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { gameId: gameIdParam } = await params;
    const gameId = new ObjectId(gameIdParam);

    // Get the game
    const game = await GameService.getGame(gameId);
    if (!game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    // Verify user is in the game
    const player = game.players.find(p => p.userId.equals(new ObjectId(session.user.id)));
    if (!player) {
      return NextResponse.json(
        { error: 'Player not in game' },
        { status: 403 }
      );
    }

    const db = await getDatabase();
    
    // Reset game state
    await db.collection('games').updateOne(
      { _id: gameId },
      {
        $set: {
          status: 'active',
          currentTurn: 1,
          turnStartedAt: new Date(),
          winnerId: undefined,
          finishedAt: undefined
        },
        $unset: {
          winnerId: "",
          finishedAt: ""
        }
      }
    );

    // Reset all players to alive
    await db.collection('games').updateOne(
      { _id: gameId },
      {
        $set: {
          'players.$[].isAlive': true,
          'players.$[].score': 0
        }
      }
    );

    // Clear all territory ownership
    await db.collection('territories').updateMany(
      { gameId },
      {
        $unset: { ownerId: "" }
      }
    );

    // Clear all units
    await db.collection('units').deleteMany({ gameId });

    // Clear all battles
    await db.collection('battles').deleteMany({ gameId });

    // Assign starting territories
    const territories = await TerritoryService.getTerritoriesByGame(gameId);
    const players = game.players.filter(p => p.isAlive);
    
    // Assign 2-3 starting territories per player
    const territoriesPerPlayer = Math.max(2, Math.floor(territories.length / players.length));
    
    for (let i = 0; i < players.length; i++) {
      const player = players[i];
      const startIndex = i * territoriesPerPlayer;
      const endIndex = Math.min(startIndex + territoriesPerPlayer, territories.length);
      
      for (let j = startIndex; j < endIndex; j++) {
        if (territories[j]) {
          await TerritoryService.claimTerritory(territories[j]._id!, player.userId);
        }
      }
    }

    return NextResponse.json(
      { message: 'Game reset successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error resetting game:', error);
    return NextResponse.json(
      { error: 'Failed to reset game' },
      { status: 500 }
    );
  }
}
