import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ gameId: string; tribeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const db = await getDatabase();
    const { gameId: gameIdParam, tribeId } = await params;
    const gameId = new ObjectId(gameIdParam);
    const userId = new ObjectId(session.user.id);

    // Get the election
    const election = await db.collection('tribal_elections').findOne({
      gameId,
      tribeId,
      status: 'active'
    });

    if (!election) {
      return NextResponse.json({ election: null });
    }

    // Get the game to find candidate details
    const game = await db.collection('games').findOne({ _id: gameId });
    if (!game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    // Get candidate details
    const candidates = game.players.filter((player: any) => 
      election.candidates.some((candidateId: ObjectId) => 
        candidateId.equals(player.userId)
      )
    );

    // Check if current user has voted
    const hasVoted = election.votes.some((vote: any) => 
      vote.voterId.equals(userId)
    );

    return NextResponse.json({
      election,
      candidates,
      hasVoted
    });
  } catch (error) {
    console.error('Error fetching election:', error);
    return NextResponse.json(
      { error: 'Failed to fetch election data' },
      { status: 500 }
    );
  }
}
