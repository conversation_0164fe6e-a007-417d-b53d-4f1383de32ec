import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { GameService } from '@/lib/database/games';
import { ObjectId } from 'mongodb';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ gameId: string; tribeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { candidateId } = await request.json();

    if (!candidateId) {
      return NextResponse.json(
        { error: 'Candidate ID is required' },
        { status: 400 }
      );
    }

    const { gameId: gameIdParam, tribeId } = await params;
    const gameId = new ObjectId(gameIdParam);
    const voterId = new ObjectId(session.user.id);
    const candidateObjectId = new ObjectId(candidateId);

    const success = await GameService.voteForTribalLeader(
      gameId,
      voterId,
      candidateObjectId,
      tribeId
    );

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to cast vote. You may not be eligible to vote or the election may have ended.' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'Vote cast successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error casting vote:', error);
    return NextResponse.json(
      { error: 'Failed to cast vote' },
      { status: 500 }
    );
  }
}
