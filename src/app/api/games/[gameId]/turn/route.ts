import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { GameEngine } from '@/lib/game/engine';
import { GameService } from '@/lib/database/games';
import { ObjectId } from 'mongodb';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ gameId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { gameId: gameIdParam } = await params;
    const gameId = new ObjectId(gameIdParam);
    const userId = new ObjectId(session.user.id);

    // Verify user is in the game
    const game = await GameService.getGame(gameId);
    if (!game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    const player = game.players.find(p => p.userId.equals(userId));
    if (!player) {
      return NextResponse.json(
        { error: 'Player not in game' },
        { status: 403 }
      );
    }

    // Check if game is active
    if (game.status !== 'active') {
      return NextResponse.json(
        { error: 'Game is not active' },
        { status: 400 }
      );
    }

    // Process the turn
    await GameEngine.processTurn(gameId);

    return NextResponse.json(
      { message: 'Turn processed successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error processing turn:', error);
    return NextResponse.json(
      { error: 'Failed to process turn' },
      { status: 500 }
    );
  }
}
