import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { UnitService } from '@/lib/database/units';
import { GameService } from '@/lib/database/games';
import { ObjectId } from 'mongodb';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ gameId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const playerId = searchParams.get('playerId');

    const { gameId: gameIdParam } = await params;
    const gameId = new ObjectId(gameIdParam);
    
    let units;
    if (playerId) {
      units = await UnitService.getUnitsByOwner(gameId, new ObjectId(playerId));
    } else {
      units = await UnitService.getUnitsByGame(gameId);
    }

    return NextResponse.json(units);
  } catch (error) {
    console.error('Error fetching units:', error);
    return NextResponse.json(
      { error: 'Failed to fetch units' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ gameId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { unitType, position } = await request.json();

    if (!unitType || !position) {
      return NextResponse.json(
        { error: 'Unit type and position are required' },
        { status: 400 }
      );
    }

    const { gameId: gameIdParam } = await params;
    const gameId = new ObjectId(gameIdParam);
    const userId = new ObjectId(session.user.id);

    // Verify user is in the game
    const game = await GameService.getGame(gameId);
    if (!game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    const player = game.players.find(p => p.userId.equals(userId));
    if (!player) {
      return NextResponse.json(
        { error: 'Player not in game' },
        { status: 403 }
      );
    }

    // Check if game is active
    if (game.status !== 'active') {
      return NextResponse.json(
        { error: 'Game is not active' },
        { status: 400 }
      );
    }

    // Create the unit
    const unitId = await UnitService.createUnit(gameId, userId, unitType, position);

    if (!unitId) {
      return NextResponse.json(
        { error: 'Failed to create unit' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { unitId: unitId.toString(), message: 'Unit created successfully' },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating unit:', error);
    return NextResponse.json(
      { error: 'Failed to create unit' },
      { status: 500 }
    );
  }
}
