import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { GameService } from '@/lib/database/games';
import { ObjectId } from 'mongodb';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ gameId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { gameId: gameIdParam } = await params;
    const gameId = new ObjectId(gameIdParam);
    const userId = new ObjectId(session.user.id);

    // Verify user is in the game
    const game = await GameService.getGame(gameId);
    if (!game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    const player = game.players.find(p => p.userId.equals(userId));
    if (!player) {
      return NextResponse.json(
        { error: 'Player not in game' },
        { status: 403 }
      );
    }

    // Check if game can be started
    if (game.status !== 'waiting') {
      return NextResponse.json(
        { error: 'Game is not in waiting status' },
        { status: 400 }
      );
    }

    if (game.currentPlayers < 2) {
      return NextResponse.json(
        { error: 'Need at least 2 players to start the game' },
        { status: 400 }
      );
    }

    // Start the game
    const success = await GameService.startGame(gameId);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to start game' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { message: 'Game started successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error starting game:', error);
    return NextResponse.json(
      { error: 'Failed to start game' },
      { status: 500 }
    );
  }
}
