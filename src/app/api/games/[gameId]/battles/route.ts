import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { BattleService } from '@/lib/database/battles';
import { ObjectId } from 'mongodb';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ gameId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const playerId = searchParams.get('playerId');
    const pending = searchParams.get('pending') === 'true';

    const { gameId: gameIdParam } = await params;
    const gameId = new ObjectId(gameIdParam);
    
    let battles;
    if (pending) {
      battles = await BattleService.getPendingBattles(gameId);
    } else if (playerId) {
      battles = await BattleService.getBattlesByPlayer(gameId, new ObjectId(playerId));
    } else {
      battles = await BattleService.getBattlesByGame(gameId);
    }

    return NextResponse.json(battles);
  } catch (error) {
    console.error('Error fetching battles:', error);
    return NextResponse.json(
      { error: 'Failed to fetch battles' },
      { status: 500 }
    );
  }
}
