import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { BattleService } from '@/lib/database/battles';
import { GameService } from '@/lib/database/games';
import { ObjectId } from 'mongodb';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ gameId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { territoryId, unitIds } = await request.json();

    if (!territoryId || !unitIds || !Array.isArray(unitIds)) {
      return NextResponse.json(
        { error: 'Territory ID and unit IDs are required' },
        { status: 400 }
      );
    }

    const { gameId: gameIdParam } = await params;
    const gameId = new ObjectId(gameIdParam);
    const attackerId = new ObjectId(session.user.id);
    const territoryObjectId = new ObjectId(territoryId);
    const unitObjectIds = unitIds.map((id: string) => new ObjectId(id));

    // Verify user is in the game
    const game = await GameService.getGame(gameId);
    if (!game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    const player = game.players.find(p => p.userId.equals(attackerId));
    if (!player) {
      return NextResponse.json(
        { error: 'Player not in game' },
        { status: 403 }
      );
    }

    // Check if game is active
    if (game.status !== 'active') {
      return NextResponse.json(
        { error: 'Game is not active' },
        { status: 400 }
      );
    }

    // Initiate the attack
    const result = await BattleService.initiateAttack(
      gameId,
      attackerId,
      territoryObjectId,
      unitObjectIds
    );

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        message: 'Attack initiated successfully',
        battleId: result.battleId?.toString()
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error initiating attack:', error);
    return NextResponse.json(
      { error: 'Failed to initiate attack' },
      { status: 500 }
    );
  }
}
