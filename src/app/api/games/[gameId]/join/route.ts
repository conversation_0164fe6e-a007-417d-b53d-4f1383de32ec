import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { GameService } from '@/lib/database/games';
import { ObjectId } from 'mongodb';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ gameId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { tribeId } = await request.json();

    if (!tribeId) {
      return NextResponse.json(
        { error: 'Tribe selection is required' },
        { status: 400 }
      );
    }

    const { gameId: gameIdParam } = await params;
    const gameId = new ObjectId(gameIdParam);
    const userId = new ObjectId(session.user.id);

    const success = await GameService.joinGame(
      gameId,
      userId,
      session.user.name,
      tribeId
    );

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to join game. Game may be full or tribe already taken.' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'Successfully joined game' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error joining game:', error);
    return NextResponse.json(
      { error: 'Failed to join game' },
      { status: 500 }
    );
  }
}
