'use client';

import { useSession, signOut } from 'next-auth/react';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import GameLobby from '@/components/GameLobby';
import TribeSelector from '@/components/TribeSelector';
import GameRoom from '@/components/GameRoom';

export default function Home() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [currentView, setCurrentView] = useState<'lobby' | 'join-game' | 'in-game'>('lobby');
  const [selectedGameId, setSelectedGameId] = useState<string>('');
  const [selectedTribe, setSelectedTribe] = useState<string>('');
  const [joiningGame, setJoiningGame] = useState(false);
  const [currentGameId, setCurrentGameId] = useState<string>('');
  const [checkingUserGames, setCheckingUserGames] = useState(true);

  // Check if user is already in any games
  useEffect(() => {
    const checkUserGames = async () => {
      if (session?.user?.id) {
        try {
          const response = await fetch('/api/games/user');
          if (response.ok) {
            const userGames = await response.json();
            if (userGames.length > 0) {
              // User is already in a game, redirect to the first active game
              const activeGame = userGames.find((game: any) => game.status === 'active') || userGames[0];
              setCurrentGameId(activeGame._id.toString());
              setCurrentView('in-game');
            }
          }
        } catch (error) {
          console.error('Error checking user games:', error);
        } finally {
          setCheckingUserGames(false);
        }
      } else {
        setCheckingUserGames(false);
      }
    };

    if (status === 'authenticated') {
      checkUserGames();
    }
  }, [session, status]);

  if (status === 'loading' || checkingUserGames) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  if (!session) {
    router.push('/auth/signin');
    return null;
  }

  const handleJoinGame = async (gameId: string) => {
    // First check if user is already in this game
    try {
      const response = await fetch(`/api/games/${gameId}/status`);
      if (response.ok) {
        const gameStatus = await response.json();
        if (gameStatus.isPlayerInGame) {
          // User is already in this game, redirect to game room
          setCurrentGameId(gameId);
          setCurrentView('in-game');
          return;
        }
      }
    } catch (error) {
      console.error('Error checking game status:', error);
    }

    // User is not in the game, show join interface
    setSelectedGameId(gameId);
    setCurrentView('join-game');
  };

  const handleTribeSelect = async (tribeId: string) => {
    setSelectedTribe(tribeId);
  };

  const confirmJoinGame = async () => {
    if (!selectedTribe || !selectedGameId) return;

    setJoiningGame(true);
    try {
      const response = await fetch(`/api/games/${selectedGameId}/join`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tribeId: selectedTribe,
        }),
      });

      if (response.ok) {
        // Successfully joined game
        setCurrentGameId(selectedGameId);
        setCurrentView('in-game');
        setSelectedTribe('');
        setSelectedGameId('');
      } else {
        const data = await response.json();
        alert(data.error || 'Failed to join game');
      }
    } catch (error) {
      console.error('Error joining game:', error);
      alert('Failed to join game');
    } finally {
      setJoiningGame(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-gray-900">African War</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Welcome, {session.user.name}
              </span>
              <button
                onClick={() => signOut()}
                className="text-sm text-gray-600 hover:text-gray-900"
              >
                Sign out
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {currentView === 'lobby' && (
          <GameLobby onJoinGame={handleJoinGame} />
        )}

        {currentView === 'join-game' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">Join Game</h2>
              <button
                onClick={() => {
                  setCurrentView('lobby');
                  setSelectedTribe('');
                  setSelectedGameId('');
                }}
                className="text-gray-600 hover:text-gray-900"
              >
                ← Back to Lobby
              </button>
            </div>

            <TribeSelector
              onTribeSelect={handleTribeSelect}
              selectedTribe={selectedTribe}
            />

            {selectedTribe && (
              <div className="text-center">
                <button
                  onClick={confirmJoinGame}
                  disabled={joiningGame}
                  className={`
                    px-6 py-3 rounded-lg font-semibold transition-colors
                    ${joiningGame
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-green-600 text-white hover:bg-green-700'
                    }
                  `}
                >
                  {joiningGame ? 'Joining...' : 'Join Game with Selected Tribe'}
                </button>
              </div>
            )}
          </div>
        )}

        {currentView === 'in-game' && currentGameId && (
          <GameRoom
            gameId={currentGameId}
            onLeaveGame={() => {
              setCurrentView('lobby');
              setCurrentGameId('');
            }}
          />
        )}
      </main>
    </div>
  );
}
