import { ObjectId } from 'mongodb';
import { GameService } from '@/lib/database/games';
import { TerritoryService } from '@/lib/database/territories';
import { UnitService } from '@/lib/database/units';
import { BattleService } from '@/lib/database/battles';
import { UserService } from '@/lib/database/users';
import { Game, GamePlayer } from '@/types/game';

export class GameEngine {
  static async processTurn(gameId: ObjectId): Promise<void> {
    const game = await GameService.getGame(gameId);
    if (!game || game.status !== 'active') return;

    try {
      // 1. Generate resources for all territories
      await TerritoryService.generateResources(gameId);

      // 2. Reset unit movement
      await UnitService.resetMovement(gameId);

      // 3. Process pending battles
      await this.processPendingBattles(gameId);

      // 4. Update player resources from territories
      await this.updatePlayerResources(gameId);

      // 5. Check win conditions
      const winner = await this.checkWinConditions(gameId);
      if (winner) {
        await this.endGame(gameId, winner);
        return;
      }

      // 6. Advance turn
      await this.advanceTurn(gameId);

    } catch (error) {
      console.error('Error processing turn:', error);
    }
  }

  private static async processPendingBattles(gameId: ObjectId): Promise<void> {
    const pendingBattles = await BattleService.getPendingBattles(gameId);
    
    for (const battle of pendingBattles) {
      await BattleService.resolveBattle(battle._id!);
    }
  }

  private static async updatePlayerResources(gameId: ObjectId): Promise<void> {
    const game = await GameService.getGame(gameId);
    if (!game) return;

    const territories = await TerritoryService.getTerritoriesByGame(gameId);

    for (const player of game.players) {
      const playerTerritories = territories.filter(t => 
        t.ownerId && t.ownerId.equals(player.userId)
      );

      let totalResources = {
        oil: player.resources.oil,
        minerals: player.resources.minerals,
        water: player.resources.water,
        food: player.resources.food
      };

      // Add resources from territories
      for (const territory of playerTerritories) {
        totalResources.oil += Math.floor(territory.resources.oil * 0.1); // 10% per turn
        totalResources.minerals += Math.floor(territory.resources.minerals * 0.1);
        totalResources.water += Math.floor(territory.resources.water * 0.1);
        totalResources.food += Math.floor(territory.resources.food * 0.1);
      }

      // Apply tribal bonuses
      const tribeBonus = this.calculateTribalBonus(player.tribeId, totalResources);
      totalResources = {
        oil: totalResources.oil + tribeBonus.oil,
        minerals: totalResources.minerals + tribeBonus.minerals,
        water: totalResources.water + tribeBonus.water,
        food: totalResources.food + tribeBonus.food
      };

      // Update player resources in database
      await this.updatePlayerResourcesInDB(gameId, player.userId, totalResources);
    }
  }

  private static calculateTribalBonus(tribeId: string, resources: any): any {
    // Import tribes data and calculate bonuses
    const { getTribeById } = require('@/data/tribes');
    const tribe = getTribeById(tribeId);
    
    const bonus = { oil: 0, minerals: 0, water: 0, food: 0 };
    
    if (tribe && tribe.bonuses) {
      for (const tribeBonus of tribe.bonuses) {
        if (tribeBonus.resource) {
          const baseAmount = resources[tribeBonus.resource] || 0;
          bonus[tribeBonus.resource] = Math.floor(baseAmount * (tribeBonus.bonus / 100));
        }
      }
    }
    
    return bonus;
  }

  private static async updatePlayerResourcesInDB(
    gameId: ObjectId, 
    playerId: ObjectId, 
    resources: any
  ): Promise<void> {
    // This would need to be implemented in GameService
    // For now, we'll skip the actual database update
  }

  private static async checkWinConditions(gameId: ObjectId): Promise<ObjectId | null> {
    const game = await GameService.getGame(gameId);
    if (!game) return null;

    // Don't check win conditions for the first few turns to let players establish themselves
    if (game.currentTurn < 3) return null;

    const territories = await TerritoryService.getTerritoriesByGame(gameId);
    const totalTerritories = territories.length;
    const requiredTerritories = Math.ceil(totalTerritories * 0.6); // Need 60% of territories

    // Check if any player controls enough territories
    for (const player of game.players) {
      if (!player.isAlive) continue;

      const playerTerritories = territories.filter(t =>
        t.ownerId && t.ownerId.equals(player.userId)
      );

      if (playerTerritories.length >= requiredTerritories) {
        return player.userId;
      }
    }

    // Check if only one player remains alive (not just one tribe)
    const alivePlayers = game.players.filter(p => p.isAlive);

    if (alivePlayers.length === 1) {
      return alivePlayers[0].userId;
    }

    // Check if only one tribe remains (and game has been going for a while)
    if (game.currentTurn >= 10) {
      const aliveTribes = new Set(alivePlayers.map(p => p.tribeId));

      if (aliveTribes.size === 1) {
        // Find the highest scoring player from the remaining tribe
        const winner = alivePlayers.reduce((prev, current) =>
          current.score > prev.score ? current : prev
        );
        return winner.userId;
      }
    }
    }

    // Check for elimination (no territories or units)
    for (const player of game.players) {
      if (!player.isAlive) continue;

      const playerTerritories = territories.filter(t => 
        t.ownerId && t.ownerId.equals(player.userId)
      );
      const playerUnits = await UnitService.getUnitsByOwner(gameId, player.userId);

      if (playerTerritories.length === 0 && playerUnits.length === 0) {
        await this.eliminatePlayer(gameId, player.userId);
      }
    }

    return null;
  }

  private static async eliminatePlayer(gameId: ObjectId, playerId: ObjectId): Promise<void> {
    // Mark player as eliminated
    // This would need to be implemented in GameService
    console.log(`Player ${playerId} has been eliminated from game ${gameId}`);
  }

  private static async endGame(gameId: ObjectId, winnerId: ObjectId): Promise<void> {
    const game = await GameService.getGame(gameId);
    if (!game) return;

    // Update game status
    // This would need to be implemented in GameService

    // Update player statistics
    for (const player of game.players) {
      const won = player.userId.equals(winnerId);
      await UserService.updateUserStats(player.userId, won, player.score);
    }

    console.log(`Game ${gameId} ended. Winner: ${winnerId}`);
  }

  private static async advanceTurn(gameId: ObjectId): Promise<void> {
    // This would need to be implemented in GameService
    console.log(`Advanced turn for game ${gameId}`);
  }

  static async handlePlayerDefeat(
    gameId: ObjectId, 
    defeatedPlayerId: ObjectId, 
    victorPlayerId: ObjectId
  ): Promise<void> {
    const game = await GameService.getGame(gameId);
    if (!game) return;

    const defeatedPlayer = game.players.find(p => p.userId.equals(defeatedPlayerId));
    const victorPlayer = game.players.find(p => p.userId.equals(victorPlayerId));

    if (!defeatedPlayer || !victorPlayer) return;

    // Check if they're from the same tribe
    if (defeatedPlayer.tribeId === victorPlayer.tribeId) {
      // Same tribe - cannot attack each other, this shouldn't happen
      return;
    }

    // Offer choice to defeated player: join victor's tribe or lose everything
    await this.offerTribalChoice(gameId, defeatedPlayerId, victorPlayerId);
  }

  private static async offerTribalChoice(
    gameId: ObjectId,
    defeatedPlayerId: ObjectId,
    victorPlayerId: ObjectId
  ): Promise<void> {
    // This would create a choice event for the defeated player
    // They can either:
    // 1. Join the victor's tribe (keep some resources/units)
    // 2. Start over with nothing but stay in their original tribe
    
    console.log(`Offering tribal choice to player ${defeatedPlayerId}`);
  }

  static async processTribalChoice(
    gameId: ObjectId,
    playerId: ObjectId,
    choice: 'join' | 'restart',
    targetTribeId?: string
  ): Promise<void> {
    if (choice === 'join' && targetTribeId) {
      await this.switchPlayerTribe(gameId, playerId, targetTribeId);
    } else {
      await this.restartPlayer(gameId, playerId);
    }
  }

  private static async switchPlayerTribe(
    gameId: ObjectId,
    playerId: ObjectId,
    newTribeId: string
  ): Promise<void> {
    // Transfer player to new tribe, keep 50% of resources
    console.log(`Player ${playerId} switching to tribe ${newTribeId}`);
  }

  private static async restartPlayer(gameId: ObjectId, playerId: ObjectId): Promise<void> {
    // Reset player to starting conditions
    console.log(`Player ${playerId} restarting from zero`);
  }

  static calculateScore(player: GamePlayer, territories: any[], units: any[]): number {
    let score = 0;

    // Points for territories
    score += territories.length * 100;

    // Points for territory types
    territories.forEach(territory => {
      switch (territory.type) {
        case 'capital':
          score += 200;
          break;
        case 'mining':
          score += 150;
          break;
        case 'port':
        case 'airfield':
          score += 100;
          break;
        default:
          score += 50;
      }
    });

    // Points for units
    score += units.length * 50;

    // Points for resources
    score += Math.floor(
      (player.resources.oil + player.resources.minerals + 
       player.resources.water + player.resources.food) / 10
    );

    // Bonus for tribal leadership
    if (player.isTribalLeader) {
      score += 500;
    }

    return score;
  }
}
