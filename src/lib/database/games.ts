import { ObjectId } from 'mongodb';
import { getDatabase } from '@/lib/mongodb';
import { Game, GamePlayer, Territory, Unit, Battle, TribalLeaderElection } from '@/types/game';
import { TRIBES, getTribeById } from '@/data/tribes';

export class GameService {
  private static async getCollection() {
    const db = await getDatabase();
    return db.collection<Game>('games');
  }

  private static async getTerritoriesCollection() {
    const db = await getDatabase();
    return db.collection<Territory>('territories');
  }

  private static async getUnitsCollection() {
    const db = await getDatabase();
    return db.collection<Unit>('units');
  }

  private static async getBattlesCollection() {
    const db = await getDatabase();
    return db.collection<Battle>('battles');
  }

  private static async getElectionsCollection() {
    const db = await getDatabase();
    return db.collection<TribalLeaderElection>('tribal_elections');
  }

  static async createGame(name: string, maxPlayers: number = 20): Promise<ObjectId> {
    const collection = await this.getCollection();
    
    const game: Game = {
      name,
      status: 'waiting',
      maxPlayers,
      currentPlayers: 0,
      players: [],
      createdAt: new Date(),
      currentTurn: 0,
      turnTimeLimit: 300, // 5 minutes per turn
      settings: {
        mapSize: { width: 50, height: 50 },
        resourceMultiplier: 1.0,
        combatMultiplier: 1.0
      }
    };

    const result = await collection.insertOne(game);
    
    // Initialize territories for the game
    await this.initializeGameTerritories(result.insertedId);
    
    return result.insertedId;
  }

  static async joinGame(gameId: ObjectId, userId: ObjectId, username: string, tribeId: string): Promise<boolean> {
    const collection = await this.getCollection();
    const game = await collection.findOne({ _id: gameId });
    
    if (!game || game.status !== 'waiting' || game.currentPlayers >= game.maxPlayers) {
      return false;
    }

    // Check if tribe is already taken
    const tribeAlreadyTaken = game.players.some(p => p.tribeId === tribeId);
    if (tribeAlreadyTaken) {
      return false;
    }

    // Check if user is already in the game
    const userAlreadyInGame = game.players.some(p => p.userId.equals(userId));
    if (userAlreadyInGame) {
      return false;
    }

    const tribe = getTribeById(tribeId);
    if (!tribe) {
      return false;
    }

    const newPlayer: GamePlayer = {
      userId,
      username,
      tribeId,
      isTribalLeader: false, // Will be determined by election if multiple players choose same tribe
      isAlive: true,
      joinedAt: new Date(),
      resources: {
        oil: 100,
        minerals: 100,
        water: 100,
        food: 100
      },
      territories: [],
      units: [],
      score: 0
    };

    await collection.updateOne(
      { _id: gameId },
      {
        $push: { players: newPlayer },
        $inc: { currentPlayers: 1 }
      }
    );

    // Check if we need to start tribal leader elections
    await this.checkTribalLeaderElections(gameId);

    return true;
  }

  static async checkTribalLeaderElections(gameId: ObjectId): Promise<void> {
    const collection = await this.getCollection();
    const electionsCollection = await this.getElectionsCollection();
    
    const game = await collection.findOne({ _id: gameId });
    if (!game) return;

    // Group players by tribe
    const tribeGroups: { [tribeId: string]: GamePlayer[] } = {};
    game.players.forEach(player => {
      if (!tribeGroups[player.tribeId]) {
        tribeGroups[player.tribeId] = [];
      }
      tribeGroups[player.tribeId].push(player);
    });

    // Create elections for tribes with 3+ players
    for (const [tribeId, players] of Object.entries(tribeGroups)) {
      if (players.length >= 3) {
        // Check if election already exists
        const existingElection = await electionsCollection.findOne({
          gameId,
          tribeId,
          status: 'active'
        });

        if (!existingElection) {
          const election: TribalLeaderElection = {
            gameId,
            tribeId,
            candidates: players.map(p => p.userId),
            votes: [],
            status: 'active',
            createdAt: new Date()
          };

          await electionsCollection.insertOne(election);
        }
      } else if (players.length === 1) {
        // Automatically make single player the tribal leader
        await collection.updateOne(
          { _id: gameId, 'players.userId': players[0].userId },
          { $set: { 'players.$.isTribalLeader': true } }
        );
      }
    }
  }

  static async voteForTribalLeader(gameId: ObjectId, voterId: ObjectId, candidateId: ObjectId, tribeId: string): Promise<boolean> {
    const electionsCollection = await this.getElectionsCollection();
    
    const election = await electionsCollection.findOne({
      gameId,
      tribeId,
      status: 'active'
    });

    if (!election) return false;

    // Check if voter is eligible (same tribe)
    if (!election.candidates.some(c => c.equals(voterId))) return false;

    // Check if candidate is valid
    if (!election.candidates.some(c => c.equals(candidateId))) return false;

    // Remove any existing vote from this voter
    await electionsCollection.updateOne(
      { _id: election._id },
      { $pull: { votes: { voterId } } }
    );

    // Add new vote
    await electionsCollection.updateOne(
      { _id: election._id },
      { $push: { votes: { voterId, candidateId } } }
    );

    // Check if all eligible voters have voted
    const updatedElection = await electionsCollection.findOne({ _id: election._id });
    if (updatedElection && updatedElection.votes.length === updatedElection.candidates.length) {
      await this.resolveTribalLeaderElection(election._id);
    }

    return true;
  }

  private static async resolveTribalLeaderElection(electionId: ObjectId): Promise<void> {
    const electionsCollection = await this.getElectionsCollection();
    const gamesCollection = await this.getCollection();
    
    const election = await electionsCollection.findOne({ _id: electionId });
    if (!election) return;

    // Count votes
    const voteCounts: { [candidateId: string]: number } = {};
    election.votes.forEach(vote => {
      const candidateIdStr = vote.candidateId.toString();
      voteCounts[candidateIdStr] = (voteCounts[candidateIdStr] || 0) + 1;
    });

    // Find winner (most votes)
    let winnerId: ObjectId | undefined;
    let maxVotes = 0;
    
    for (const [candidateIdStr, votes] of Object.entries(voteCounts)) {
      if (votes > maxVotes) {
        maxVotes = votes;
        winnerId = new ObjectId(candidateIdStr);
      }
    }

    if (winnerId) {
      // Update election
      await electionsCollection.updateOne(
        { _id: electionId },
        {
          $set: {
            status: 'completed',
            winnerId,
            completedAt: new Date()
          }
        }
      );

      // Update game to set tribal leader
      await gamesCollection.updateOne(
        { _id: election.gameId, 'players.userId': winnerId },
        { $set: { 'players.$.isTribalLeader': true } }
      );
    }
  }

  private static async initializeGameTerritories(gameId: ObjectId): Promise<void> {
    const { TerritoryService } = await import('./territories');
    await TerritoryService.createInitialTerritories(gameId);
  }

  static async getGame(gameId: ObjectId): Promise<Game | null> {
    const collection = await this.getCollection();
    return await collection.findOne({ _id: gameId });
  }

  static async getActiveGames(): Promise<Game[]> {
    const collection = await this.getCollection();
    return await collection.find({ status: { $in: ['waiting', 'active'] } }).toArray();
  }

  static async getUserActiveGames(userId: ObjectId): Promise<Game[]> {
    const collection = await this.getCollection();
    return await collection.find({
      status: { $in: ['waiting', 'active'] },
      'players.userId': userId
    }).toArray();
  }

  static async startGame(gameId: ObjectId): Promise<boolean> {
    const collection = await this.getCollection();
    
    const result = await collection.updateOne(
      { _id: gameId, status: 'waiting' },
      {
        $set: {
          status: 'active',
          startedAt: new Date(),
          turnStartedAt: new Date()
        }
      }
    );

    return result.modifiedCount > 0;
  }
}
