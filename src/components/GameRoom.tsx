'use client';

import { useState, useEffect } from 'react';
import { useSocket } from '@/hooks/useSocket';
import { Game, GamePlayer, Territory } from '@/types/game';
import { useSession } from 'next-auth/react';
import TribalElection from './TribalElection';
import GameMap from './GameMap';
import TerritoryDetails from './TerritoryDetails';
import UnitManagement from './UnitManagement';
import BattleHistory from './BattleHistory';
import GameStats from './GameStats';
import GameNavigation from './GameNavigation';
import Diplomacy from './Diplomacy';
import GameControls from './GameControls';

interface GameRoomProps {
  gameId: string;
  onLeaveGame: () => void;
}

export default function GameRoom({ gameId, onLeaveGame }: GameRoomProps) {
  const { data: session } = useSession();
  const [gameState, setGameState] = useState<Game | null>(null);
  const [connectedPlayers, setConnectedPlayers] = useState<Set<string>>(new Set());
  const [notifications, setNotifications] = useState<string[]>([]);
  const [territories, setTerritories] = useState<Territory[]>([]);
  const [selectedTerritory, setSelectedTerritory] = useState<Territory | null>(null);
  const [showTerritoryDetails, setShowTerritoryDetails] = useState(false);
  const [activeTab, setActiveTab] = useState<'map' | 'units' | 'battles' | 'stats' | 'diplomacy'>('map');
  const [showAttackDialog, setShowAttackDialog] = useState(false);
  const [selectedUnits, setSelectedUnits] = useState<string[]>([]);
  const [playerUnits, setPlayerUnits] = useState<any[]>([]);

  const {
    isConnected,
    isConnecting,
    error,
    joinGame,
    leaveGame,
    sendGameAction,
    castTribalVote,
    on,
    off,
  } = useSocket({ gameId, autoConnect: true });

  useEffect(() => {
    // Fetch territories when component mounts
    fetchTerritories();
    // Also fetch game state directly as fallback
    fetchGameState();
    // Fetch player units
    fetchPlayerUnits();
  }, [gameId]);

  const fetchGameState = async () => {
    try {
      // Fetch full game data directly
      const gameResponse = await fetch(`/api/games/${gameId}`);
      if (gameResponse.ok) {
        const gameData = await gameResponse.json();
        setGameState(gameData);
      }
    } catch (error) {
      console.error('Error fetching game state:', error);
    }
  };

  const fetchPlayerUnits = async () => {
    if (!session?.user?.id) return;

    try {
      const response = await fetch(`/api/games/${gameId}/units?playerId=${session.user.id}`);
      if (response.ok) {
        const units = await response.json();
        setPlayerUnits(units);
      }
    } catch (error) {
      console.error('Error fetching player units:', error);
    }
  };

  useEffect(() => {
    // Set up socket event listeners
    const handleGameState = (game: Game) => {
      setGameState(game);
    };

    const handlePlayerJoined = (data: { playerId: string; playerName: string }) => {
      setConnectedPlayers(prev => new Set(prev).add(data.playerId));
      addNotification(`${data.playerName} joined the game`);
    };

    const handlePlayerLeft = (data: { playerId: string }) => {
      setConnectedPlayers(prev => {
        const newSet = new Set(prev);
        newSet.delete(data.playerId);
        return newSet;
      });
      addNotification(`Player left the game`);
    };

    const handlePlayerDisconnected = (data: { playerId: string }) => {
      setConnectedPlayers(prev => {
        const newSet = new Set(prev);
        newSet.delete(data.playerId);
        return newSet;
      });
      addNotification(`Player disconnected`);
    };

    const handleGameActionResult = (data: any) => {
      // Handle game action results
      addNotification(`${data.action} performed by player`);
      // Refresh game state or update specific parts
    };

    const handleTribalVoteCast = (data: { tribeId: string; voterId: string }) => {
      addNotification(`Vote cast in ${data.tribeId} tribal election`);
    };

    const handleError = (data: { message: string }) => {
      addNotification(`Error: ${data.message}`);
    };

    // Register event listeners
    on('game-state', handleGameState);
    on('player-joined', handlePlayerJoined);
    on('player-left', handlePlayerLeft);
    on('player-disconnected', handlePlayerDisconnected);
    on('game-action-result', handleGameActionResult);
    on('tribal-vote-cast', handleTribalVoteCast);
    on('error', handleError);

    return () => {
      // Clean up event listeners
      off('game-state', handleGameState);
      off('player-joined', handlePlayerJoined);
      off('player-left', handlePlayerLeft);
      off('player-disconnected', handlePlayerDisconnected);
      off('game-action-result', handleGameActionResult);
      off('tribal-vote-cast', handleTribalVoteCast);
      off('error', handleError);
    };
  }, [on, off]);

  const fetchTerritories = async () => {
    try {
      const response = await fetch(`/api/games/${gameId}/territories`);
      if (response.ok) {
        const data = await response.json();
        setTerritories(data);
      }
    } catch (error) {
      console.error('Error fetching territories:', error);
    }
  };

  const addNotification = (message: string) => {
    setNotifications(prev => [...prev.slice(-4), message]); // Keep last 5 notifications
  };

  const handleTerritoryClick = (territory: Territory) => {
    setSelectedTerritory(territory);
    setShowTerritoryDetails(true);
  };

  const handleClaimTerritory = async () => {
    if (!selectedTerritory) return;

    try {
      const response = await fetch(`/api/games/${gameId}/territories/${selectedTerritory._id}/claim`, {
        method: 'POST',
      });

      if (response.ok) {
        addNotification(`Claimed ${selectedTerritory.name}`);
        fetchTerritories(); // Refresh territories
        setShowTerritoryDetails(false);
      } else {
        const data = await response.json();
        addNotification(`Failed to claim territory: ${data.error}`);
      }
    } catch (error) {
      console.error('Error claiming territory:', error);
      addNotification('Failed to claim territory');
    }
  };

  const handleUpgradeTerritory = async (upgradeType: string) => {
    if (!selectedTerritory) return;

    try {
      const response = await fetch(`/api/games/${gameId}/territories/${selectedTerritory._id}/upgrade`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ upgradeType }),
      });

      if (response.ok) {
        addNotification(`Upgraded ${selectedTerritory.name}`);
        fetchTerritories(); // Refresh territories
        setShowTerritoryDetails(false);
      } else {
        const data = await response.json();
        addNotification(`Failed to upgrade territory: ${data.error}`);
      }
    } catch (error) {
      console.error('Error upgrading territory:', error);
      addNotification('Failed to upgrade territory');
    }
  };

  const handleAttackTerritory = () => {
    if (!selectedTerritory) return;

    // Check if territory can be attacked
    if (selectedTerritory.ownerId?.toString() === session?.user?.id) {
      addNotification("Cannot attack your own territory!");
      return;
    }

    setShowAttackDialog(true);
    setShowTerritoryDetails(false);
  };

  const executeAttack = async () => {
    if (!selectedTerritory || selectedUnits.length === 0) return;

    try {
      const response = await fetch(`/api/games/${gameId}/attack`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          territoryId: selectedTerritory._id,
          unitIds: selectedUnits,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        addNotification(`Attack on ${selectedTerritory.name} initiated!`);
        setShowAttackDialog(false);
        setSelectedUnits([]);
        fetchTerritories(); // Refresh territories
        fetchPlayerUnits(); // Refresh units
      } else {
        const data = await response.json();
        addNotification(`Attack failed: ${data.error}`);
      }
    } catch (error) {
      console.error('Error attacking territory:', error);
      addNotification('Failed to attack territory');
    }
  };

  const handleLeaveGame = () => {
    leaveGame();
    onLeaveGame();
  };

  const currentPlayer = gameState?.players.find(p => p.userId.toString() === session?.user?.id);
  const isOwned = selectedTerritory?.ownerId?.toString() === session?.user?.id;
  const canClaim = !selectedTerritory?.ownerId && gameState?.status === 'active';

  if (isConnecting) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">Connecting to game...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-lg mb-4">Connection Error</div>
          <div className="text-gray-600 mb-4">{error}</div>
          <button
            onClick={handleLeaveGame}
            className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700"
          >
            Back to Lobby
          </button>
        </div>
      </div>
    );
  }

  if (!gameState) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">Loading game...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-bold text-gray-900">{gameState.name}</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Turn {gameState.currentTurn}
              </span>
              <button
                onClick={handleLeaveGame}
                className="text-sm text-gray-600 hover:text-gray-900"
              >
                Leave Game
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <GameNavigation
        activeTab={activeTab}
        onTabChange={setActiveTab}
        gameStatus={gameState.status}
        isConnected={isConnected}
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main Game Area */}
          <div className="lg:col-span-3 space-y-6">
            {/* Game Status */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold mb-4">Game Status</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <div className="text-sm text-gray-600">Players</div>
                  <div className="font-semibold">{gameState.currentPlayers}/{gameState.maxPlayers}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Status</div>
                  <div className="font-semibold capitalize">{gameState.status}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Turn</div>
                  <div className="font-semibold">{gameState.currentTurn}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Connected</div>
                  <div className="font-semibold">{connectedPlayers.size}</div>
                </div>
              </div>
            </div>

            {/* Player Resources */}
            {currentPlayer && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-lg font-semibold mb-4">Your Resources</h2>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">{currentPlayer.resources.oil}</div>
                    <div className="text-sm text-gray-600">Oil</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-600">{currentPlayer.resources.minerals}</div>
                    <div className="text-sm text-gray-600">Minerals</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{currentPlayer.resources.water}</div>
                    <div className="text-sm text-gray-600">Water</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{currentPlayer.resources.food}</div>
                    <div className="text-sm text-gray-600">Food</div>
                  </div>
                </div>
              </div>
            )}

            {/* Main Content Area */}
            <div className="bg-white rounded-lg shadow-md p-6">
              {/* Tab Content */}
              {activeTab === 'map' && (
                <GameMap
                  gameId={gameId}
                  territories={territories}
                  onTerritoryClick={handleTerritoryClick}
                  selectedTerritory={selectedTerritory}
                />
              )}

              {activeTab === 'units' && currentPlayer && (
                <UnitManagement
                  gameId={gameId}
                  playerId={session?.user?.id || ''}
                  playerResources={currentPlayer.resources}
                  onUnitCreate={() => {
                    addNotification('New unit recruited!');
                  }}
                />
              )}

              {activeTab === 'battles' && (
                <BattleHistory
                  gameId={gameId}
                  playerId={session?.user?.id}
                />
              )}

              {activeTab === 'stats' && (
                <GameStats
                  game={gameState}
                  currentPlayer={currentPlayer}
                />
              )}

              {activeTab === 'diplomacy' && currentPlayer && (
                <Diplomacy
                  gameId={gameId}
                  currentPlayer={currentPlayer}
                  allPlayers={gameState.players}
                />
              )}
            </div>

            {/* Tribal Election */}
            {currentPlayer && (
              <TribalElection
                gameId={gameId}
                playerId={session?.user?.id || ''}
                tribeId={currentPlayer.tribeId}
                tribeName={currentPlayer.tribeId}
              />
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Game Controls */}
            <GameControls
              game={gameState}
              currentPlayer={currentPlayer}
              onGameAction={(message) => addNotification(message)}
            />

            {/* Territory Details */}
            {showTerritoryDetails && selectedTerritory && (
              <TerritoryDetails
                territory={selectedTerritory}
                isOwned={isOwned}
                canClaim={canClaim}
                canAttack={selectedTerritory.ownerId && selectedTerritory.ownerId?.toString() !== session?.user?.id && gameState?.status === 'active'}
                onClaim={handleClaimTerritory}
                onUpgrade={handleUpgradeTerritory}
                onAttack={handleAttackTerritory}
                onClose={() => setShowTerritoryDetails(false)}
              />
            )}

            {/* Attack Dialog */}
            {showAttackDialog && selectedTerritory && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white rounded-lg shadow-lg p-6 max-w-md w-full mx-4">
                  <h3 className="text-xl font-bold mb-4">Attack {selectedTerritory.name}</h3>

                  <div className="mb-4">
                    <p className="text-sm text-gray-600 mb-3">
                      Select units to attack with. Units must be within 2 tiles of the target.
                    </p>

                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {playerUnits.filter(unit => {
                        const distance = Math.abs(unit.position.x - selectedTerritory.position.x) +
                                        Math.abs(unit.position.y - selectedTerritory.position.y);
                        return distance <= 2;
                      }).map(unit => (
                        <label key={unit._id} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={selectedUnits.includes(unit._id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedUnits(prev => [...prev, unit._id]);
                              } else {
                                setSelectedUnits(prev => prev.filter(id => id !== unit._id));
                              }
                            }}
                            className="rounded"
                          />
                          <span className="text-sm">
                            {unit.name} ({unit.type}) - Attack: {unit.attack}, Health: {unit.health}/{unit.maxHealth}
                          </span>
                        </label>
                      ))}
                    </div>

                    {playerUnits.filter(unit => {
                      const distance = Math.abs(unit.position.x - selectedTerritory.position.x) +
                                      Math.abs(unit.position.y - selectedTerritory.position.y);
                      return distance <= 2;
                    }).length === 0 && (
                      <p className="text-sm text-red-600">No units within range of this territory.</p>
                    )}
                  </div>

                  <div className="flex space-x-3">
                    <button
                      onClick={executeAttack}
                      disabled={selectedUnits.length === 0}
                      className="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                    >
                      Attack ({selectedUnits.length} units)
                    </button>
                    <button
                      onClick={() => {
                        setShowAttackDialog(false);
                        setSelectedUnits([]);
                      }}
                      className="flex-1 bg-gray-500 text-white py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Players List */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold mb-4">Players</h3>
              <div className="space-y-2">
                {gameState.players.map(player => (
                  <div
                    key={player.userId.toString()}
                    className="flex items-center justify-between p-2 bg-gray-50 rounded"
                  >
                    <div>
                      <div className="font-medium">{player.username}</div>
                      <div className="text-sm text-gray-600">{player.tribeId}</div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {player.isTribalLeader && <span className="text-yellow-500">👑</span>}
                      <div className={`w-2 h-2 rounded-full ${
                        connectedPlayers.has(player.userId.toString()) ? 'bg-green-500' : 'bg-gray-400'
                      }`} />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Notifications */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
              <div className="space-y-2">
                {notifications.length === 0 ? (
                  <div className="text-sm text-gray-500">No recent activity</div>
                ) : (
                  notifications.map((notification, index) => (
                    <div key={index} className="text-sm text-gray-700 p-2 bg-gray-50 rounded">
                      {notification}
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
