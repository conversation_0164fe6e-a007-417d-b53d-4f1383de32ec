'use client';

import { useState } from 'react';
import { Territory } from '@/types/game';

interface TerritoryDetailsProps {
  territory: Territory;
  isOwned: boolean;
  canClaim: boolean;
  canAttack?: boolean;
  onClaim?: () => void;
  onUpgrade?: (upgradeType: string) => void;
  onAttack?: () => void;
  onClose?: () => void;
}

export default function TerritoryDetails({
  territory,
  isOwned,
  canClaim,
  canAttack,
  onClaim,
  onUpgrade,
  onAttack,
  onClose
}: TerritoryDetailsProps) {
  const [selectedUpgrade, setSelectedUpgrade] = useState<string>('');

  const upgradeOptions = [
    { id: 'defense', name: 'Defense Upgrade', cost: { oil: 50, minerals: 30 }, description: '+5 Defense Value' },
    { id: 'population', name: 'Population Growth', cost: { food: 100, water: 50 }, description: '+100 Population' },
    { id: 'oil_production', name: 'Oil Refinery', cost: { minerals: 80, food: 20 }, description: '+10 Oil Production' },
    { id: 'mineral_production', name: 'Mining Expansion', cost: { oil: 60, food: 30 }, description: '+10 Mineral Production' },
    { id: 'water_production', name: 'Water Treatment', cost: { oil: 40, minerals: 60 }, description: '+10 Water Production' },
    { id: 'food_production', name: 'Agricultural Development', cost: { water: 80, minerals: 40 }, description: '+10 Food Production' },
  ];

  const getTerritoryTypeDescription = (type: Territory['type']): string => {
    switch (type) {
      case 'capital': return 'Major political and economic center with balanced resource production';
      case 'mining': return 'Rich in minerals and precious metals, high mineral production';
      case 'port': return 'Coastal trading hub with oil imports and food exports';
      case 'airfield': return 'Strategic military position with fuel storage';
      case 'refugee_camp': return 'Humanitarian center providing water and basic food';
      case 'checkpoint': return 'Border control point with moderate resource flow';
      default: return 'Strategic location with basic resource production';
    }
  };

  const getResourceIcon = (resource: string): string => {
    switch (resource) {
      case 'oil': return '🛢️';
      case 'minerals': return '⚡';
      case 'water': return '💧';
      case 'food': return '🌾';
      default: return '📦';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-md">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-xl font-bold text-gray-900">{territory.name}</h3>
          <p className="text-sm text-gray-600 capitalize">
            {territory.type.replace('_', ' ')}
          </p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        )}
      </div>

      <div className="space-y-4">
        {/* Description */}
        <div>
          <p className="text-sm text-gray-700">
            {getTerritoryTypeDescription(territory.type)}
          </p>
        </div>

        {/* Status */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <span className="text-sm font-medium">Status:</span>
          <span className={`px-2 py-1 rounded text-xs font-medium ${
            territory.ownerId 
              ? isOwned 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
              : 'bg-yellow-100 text-yellow-800'
          }`}>
            {territory.ownerId 
              ? isOwned 
                ? 'Your Territory' 
                : 'Enemy Territory'
              : 'Unclaimed'
            }
          </span>
        </div>

        {/* Resources */}
        <div>
          <h4 className="font-semibold mb-2">Resources</h4>
          <div className="grid grid-cols-2 gap-2">
            {Object.entries(territory.resources).map(([resource, amount]) => (
              <div key={resource} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-sm flex items-center gap-1">
                  {getResourceIcon(resource)}
                  {resource.charAt(0).toUpperCase() + resource.slice(1)}
                </span>
                <span className="font-medium">{amount}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Territory Stats */}
        <div>
          <h4 className="font-semibold mb-2">Territory Stats</h4>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Defense Value:</span>
              <span className="font-medium">{territory.defenseValue}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Population:</span>
              <span className="font-medium">{territory.population.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Position:</span>
              <span className="font-medium">({territory.position.x}, {territory.position.y})</span>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="space-y-3">
          {/* Claim Territory */}
          {canClaim && !territory.ownerId && onClaim && (
            <button
              onClick={onClaim}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Claim Territory
            </button>
          )}

          {/* Attack Territory */}
          {canAttack && onAttack && (
            <button
              onClick={onAttack}
              className="w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors"
            >
              Attack Territory
            </button>
          )}

          {/* Upgrades */}
          {isOwned && onUpgrade && (
            <div>
              <h4 className="font-semibold mb-2">Upgrades</h4>
              <select
                value={selectedUpgrade}
                onChange={(e) => setSelectedUpgrade(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-lg mb-2"
              >
                <option value="">Select an upgrade...</option>
                {upgradeOptions.map(upgrade => (
                  <option key={upgrade.id} value={upgrade.id}>
                    {upgrade.name} - {upgrade.description}
                  </option>
                ))}
              </select>

              {selectedUpgrade && (
                <div className="space-y-2">
                  <div className="p-2 bg-gray-50 rounded">
                    <div className="text-sm font-medium mb-1">
                      {upgradeOptions.find(u => u.id === selectedUpgrade)?.name}
                    </div>
                    <div className="text-xs text-gray-600 mb-2">
                      {upgradeOptions.find(u => u.id === selectedUpgrade)?.description}
                    </div>
                    <div className="text-xs">
                      <span className="font-medium">Cost: </span>
                      {Object.entries(upgradeOptions.find(u => u.id === selectedUpgrade)?.cost || {}).map(([resource, amount], index) => (
                        <span key={resource}>
                          {index > 0 && ', '}
                          {getResourceIcon(resource)} {amount}
                        </span>
                      ))}
                    </div>
                  </div>
                  <button
                    onClick={() => onUpgrade(selectedUpgrade)}
                    className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
                  >
                    Upgrade Territory
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
