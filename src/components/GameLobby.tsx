'use client';

import { useState, useEffect } from 'react';
import { Game } from '@/types/game';
import { useSession } from 'next-auth/react';

interface GameLobbyProps {
  onJoinGame: (gameId: string) => void;
}

export default function GameLobby({ onJoinGame }: GameLobbyProps) {
  const { data: session } = useSession();
  const [games, setGames] = useState<Game[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [newGameName, setNewGameName] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [userGames, setUserGames] = useState<string[]>([]);

  useEffect(() => {
    fetchGames();
    fetchUserGames();
    const interval = setInterval(() => {
      fetchGames();
      fetchUserGames();
    }, 5000); // Refresh every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchGames = async () => {
    try {
      const response = await fetch('/api/games');
      const data = await response.json();
      setGames(data);
    } catch (error) {
      console.error('Error fetching games:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchUserGames = async () => {
    try {
      const response = await fetch('/api/games/user');
      if (response.ok) {
        const data = await response.json();
        setUserGames(data.map((game: any) => game._id.toString()));
      }
    } catch (error) {
      console.error('Error fetching user games:', error);
    }
  };

  const createGame = async () => {
    if (!newGameName.trim()) return;

    setCreating(true);
    try {
      const response = await fetch('/api/games', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newGameName,
          maxPlayers: 20,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setNewGameName('');
        setShowCreateForm(false);
        fetchGames();
        onJoinGame(data.gameId);
      }
    } catch (error) {
      console.error('Error creating game:', error);
    } finally {
      setCreating(false);
    }
  };

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="text-lg">Loading games...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">African War - Game Lobby</h1>
        <p className="text-gray-600">Join an existing game or create a new one</p>
      </div>

      {/* Create Game Section */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Create New Game</h2>
          <button
            onClick={() => setShowCreateForm(!showCreateForm)}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
          >
            {showCreateForm ? 'Cancel' : 'Create Game'}
          </button>
        </div>

        {showCreateForm && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Game Name
              </label>
              <input
                type="text"
                value={newGameName}
                onChange={(e) => setNewGameName(e.target.value)}
                placeholder="Enter game name..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <button
              onClick={createGame}
              disabled={creating || !newGameName.trim()}
              className={`
                w-full py-2 px-4 rounded-lg font-semibold transition-colors
                ${creating || !newGameName.trim()
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
                }
              `}
            >
              {creating ? 'Creating...' : 'Create Game'}
            </button>
          </div>
        )}
      </div>

      {/* Active Games */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Active Games</h2>
        
        {games.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            No active games found. Create one to get started!
          </div>
        ) : (
          <div className="space-y-4">
            {games.map(game => (
              <div
                key={game._id?.toString()}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900">{game.name}</h3>
                    <div className="text-sm text-gray-600 mt-1">
                      <p>Status: <span className={`font-medium ${
                        game.status === 'waiting' ? 'text-yellow-600' : 
                        game.status === 'active' ? 'text-green-600' : 'text-gray-600'
                      }`}>
                        {game.status.charAt(0).toUpperCase() + game.status.slice(1)}
                      </span></p>
                      <p>Players: {game.currentPlayers} / {game.maxPlayers}</p>
                      <p>Created: {new Date(game.createdAt).toLocaleDateString()}</p>
                    </div>
                    
                    {/* Show tribes taken */}
                    {game.players.length > 0 && (
                      <div className="mt-2">
                        <p className="text-sm font-medium text-gray-700">Tribes taken:</p>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {game.players.map(player => (
                            <span
                              key={player.userId.toString()}
                              className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded"
                            >
                              {player.username} ({player.tribeId})
                              {player.isTribalLeader && ' 👑'}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="ml-4">
                    {userGames.includes(game._id!.toString()) ? (
                      <button
                        onClick={() => onJoinGame(game._id!.toString())}
                        className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                      >
                        {game.status === 'active' ? 'Continue Game' : 'Enter Game'}
                      </button>
                    ) : game.status === 'waiting' && game.currentPlayers < game.maxPlayers ? (
                      <button
                        onClick={() => onJoinGame(game._id!.toString())}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        Join Game
                      </button>
                    ) : game.status === 'active' ? (
                      <span className="text-sm text-gray-500">Game in progress</span>
                    ) : (
                      <span className="text-sm text-gray-500">Game full</span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
