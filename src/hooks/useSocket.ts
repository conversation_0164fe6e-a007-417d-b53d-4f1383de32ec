'use client';

import { useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { useSession } from 'next-auth/react';

interface UseSocketOptions {
  gameId?: string;
  autoConnect?: boolean;
}

interface GameAction {
  type: string;
  payload: any;
}

interface SocketEvents {
  'game-state': (data: any) => void;
  'player-joined': (data: { playerId: string; playerName: string }) => void;
  'player-left': (data: { playerId: string }) => void;
  'player-disconnected': (data: { playerId: string }) => void;
  'game-action-result': (data: any) => void;
  'tribal-vote-cast': (data: { tribeId: string; voterId: string }) => void;
  'error': (data: { message: string }) => void;
}

export function useSocket({ gameId, autoConnect = true }: UseSocketOptions = {}) {
  const { data: session } = useSession();
  const socketRef = useRef<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const connect = () => {
    if (socketRef.current?.connected) return;

    setIsConnecting(true);
    setError(null);

    const socket = io(process.env.NODE_ENV === 'production' ? '' : 'http://localhost:3000', {
      path: '/api/socket/io',
      addTrailingSlash: false,
      timeout: 10000,
      reconnection: true,
      reconnectionDelay: 2000,
      reconnectionAttempts: 5,
    });

    socket.on('connect', () => {
      console.log('Connected to socket server');
      setIsConnected(true);
      setIsConnecting(false);
      
      // Auto-join game if gameId and session are available
      if (gameId && session?.user?.id) {
        socket.emit('join-game', {
          gameId,
          userId: session.user.id,
        });
      }
    });

    socket.on('disconnect', () => {
      console.log('Disconnected from socket server');
      setIsConnected(false);
    });

    socket.on('connect_error', (err) => {
      console.error('Socket connection error:', err);
      setError('Failed to connect to game server');
      setIsConnecting(false);
    });

    socket.on('error', (data: { message: string }) => {
      console.error('Socket error:', data.message);
      setError(data.message);
    });

    socketRef.current = socket;
  };

  const disconnect = () => {
    if (socketRef.current) {
      if (gameId) {
        socketRef.current.emit('leave-game');
      }
      socketRef.current.disconnect();
      socketRef.current = null;
      setIsConnected(false);
    }
  };

  const joinGame = (gameId: string) => {
    if (socketRef.current && session?.user?.id) {
      socketRef.current.emit('join-game', {
        gameId,
        userId: session.user.id,
      });
    }
  };

  const leaveGame = () => {
    if (socketRef.current) {
      socketRef.current.emit('leave-game');
    }
  };

  const sendGameAction = (action: GameAction) => {
    if (socketRef.current) {
      socketRef.current.emit('game-action', action);
    }
  };

  const castTribalVote = (tribeId: string, candidateId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('tribal-vote', { tribeId, candidateId });
    }
  };

  const on = <K extends keyof SocketEvents>(event: K, handler: SocketEvents[K]) => {
    if (socketRef.current) {
      socketRef.current.on(event, handler);
    }
  };

  const off = <K extends keyof SocketEvents>(event: K, handler?: SocketEvents[K]) => {
    if (socketRef.current) {
      if (handler) {
        socketRef.current.off(event, handler);
      } else {
        socketRef.current.off(event);
      }
    }
  };

  useEffect(() => {
    if (autoConnect && session?.user?.id) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [autoConnect, session?.user?.id]);

  useEffect(() => {
    // Auto-join game when gameId changes
    if (gameId && socketRef.current?.connected && session?.user?.id) {
      joinGame(gameId);
    }
  }, [gameId, session?.user?.id]);

  return {
    socket: socketRef.current,
    isConnected,
    isConnecting,
    error,
    connect,
    disconnect,
    joinGame,
    leaveGame,
    sendGameAction,
    castTribalVote,
    on,
    off,
  };
}
